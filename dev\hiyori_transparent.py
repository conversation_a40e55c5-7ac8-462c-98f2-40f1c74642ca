#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import math
import random
import time
import OpenGL.GL as gl
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtWidgets import QApplication, QOpenGLWidget, QLabel
from PyQt5.QtGui import QFont, QSurfaceFormat

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

import live2d.v3 as live2d

class HiyoriTransparentWindow(QOpenGLWidget):
    def __init__(self):
        super().__init__()
        self.model = None

        # 设置OpenGL格式 - 关键修复
        format = QSurfaceFormat()
        format.setAlphaBufferSize(8)
        format.setDepthBufferSize(24)
        format.setStencilBufferSize(8)
        format.setSamples(0)  # 禁用抗锯齿减少黑边
        format.setVersion(2, 1)
        format.setProfile(QSurfaceFormat.CompatibilityProfile)
        self.setFormat(format)

        self.init_ui()
        
    def init_ui(self):
        # 设置窗口属性
        self.setWindowTitle('Hiyori Live2D 透明窗口')
        self.setGeometry(300, 300, 600, 800)
        
        # 设置窗口背景透明
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint)
        
        # 添加控制按钮
        self.setup_controls()
        
        # 透明状态标志
        self.is_transparent = True

        # 随机动画相关
        self.motion_list = [f"Hiyori_m{i:02d}" for i in range(1, 11)]  # m01到m10
        self.last_motion_time = time.time()
        self.motion_interval = 2.0  # 2秒间隔，便于检查
        self.current_motion = "待播放..."
        
    def setup_controls(self):
        # 切换透明度按钮
        self.toggle_btn = QLabel('🔄', self)
        self.toggle_btn.setGeometry(520, 10, 30, 30)
        self.toggle_btn.setAlignment(Qt.AlignCenter)
        self.toggle_btn.setFont(QFont('Arial', 14, QFont.Bold))
        self.toggle_btn.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(0, 150, 255, 150);
                border-radius: 15px;
            }
            QLabel:hover {
                background-color: rgba(0, 150, 255, 200);
            }
        """)
        self.toggle_btn.mousePressEvent = lambda event: self.toggle_transparency()
        
        # 关闭按钮
        close_btn = QLabel('✕', self)
        close_btn.setGeometry(560, 10, 30, 30)
        close_btn.setAlignment(Qt.AlignCenter)
        close_btn.setFont(QFont('Arial', 14, QFont.Bold))
        close_btn.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255, 0, 0, 150);
                border-radius: 15px;
            }
            QLabel:hover {
                background-color: rgba(255, 0, 0, 200);
            }
        """)
        close_btn.mousePressEvent = lambda event: self.close()
        
        # 模型信息标签
        self.info_label = QLabel('Hiyori Live2D Model', self)
        self.info_label.setGeometry(10, 10, 200, 30)
        self.info_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.info_label.setFont(QFont('Arial', 10, QFont.Bold))
        self.info_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(0, 0, 0, 100);
                border-radius: 5px;
                padding: 5px;
            }
        """)

        # 动画状态标签
        self.motion_label = QLabel('动画: 待播放...', self)
        self.motion_label.setGeometry(10, 50, 250, 25)
        self.motion_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.motion_label.setFont(QFont('Arial', 9))
        self.motion_label.setStyleSheet("""
            QLabel {
                color: yellow;
                background-color: rgba(0, 0, 0, 80);
                border-radius: 3px;
                padding: 3px;
            }
        """)

    def initializeGL(self):
        try:
            # 使用正确的初始化函数
            live2d.glInit()

            # 设置OpenGL状态 - 关键修复
            gl.glEnable(gl.GL_BLEND)
            gl.glBlendFunc(gl.GL_SRC_ALPHA, gl.GL_ONE_MINUS_SRC_ALPHA)
            gl.glDisable(gl.GL_DEPTH_TEST)
            gl.glDisable(gl.GL_MULTISAMPLE)

            self.model = live2d.LAppModel()

            # 加载Hiyori模型
            model_path = os.path.join(
                os.path.dirname(__file__),
                '..',
                'Resources',
                'v3',
                'Hiyori',
                'Hiyori.model3.json'
            )

            if os.path.exists(model_path):
                self.model.LoadModelJson(model_path)
                self.info_label.setText('Hiyori Model Loaded ✓')
                self.info_label.setStyleSheet("""
                    QLabel {
                        color: white;
                        background-color: rgba(0, 150, 0, 150);
                        border-radius: 5px;
                        padding: 5px;
                    }
                """)
            else:
                self.info_label.setText('Model Not Found ✗')
                self.info_label.setStyleSheet("""
                    QLabel {
                        color: white;
                        background-color: rgba(150, 0, 0, 150);
                        border-radius: 5px;
                        padding: 5px;
                    }
                """)
                print(f"Model file not found: {model_path}")

            # 启动定时器进行动画更新
            self.timer = QTimer()
            self.timer.timeout.connect(self.update_frame)
            self.timer.start(int(1000 / 120))  # 120 FPS

            # 启动随机动画定时器
            self.motion_timer = QTimer()
            self.motion_timer.timeout.connect(self.play_random_motion)
            self.motion_timer.start(int(self.motion_interval * 1000))  # 转换为毫秒

        except Exception as e:
            print(f"Error initializing Live2D: {e}")
            self.info_label.setText('Init Error ✗')

    def paintGL(self):
        try:
            # 关键修复：alpha=0实现真正的透明
            if self.is_transparent:
                # 透明模式：alpha=0实现透明背景
                live2d.clearBuffer(0.0, 0.0, 0.0, 0.0)
            else:
                # 不透明模式：使用深灰色背景
                live2d.clearBuffer(0.2, 0.2, 0.2, 1.0)

            if self.model:
                self.model.Update()
                self.model.Draw()
        except Exception as e:
            print(f"Error drawing model: {e}")

    def update_frame(self):
        """更新帧 - 检查随机动画触发"""
        current_time = time.time()

        # 检查是否需要播放随机动画
        if current_time - self.last_motion_time > self.motion_interval:
            self.play_random_motion()
            self.last_motion_time = current_time

        self.update()

    def play_random_motion(self):
        """播放随机动画"""
        if self.model and self.motion_list:
            try:
                # 随机选择动画
                motion_name = random.choice(self.motion_list)
                self.current_motion = motion_name

                # 播放动画
                self.model.StartMotion("", motion_name, live2d.MotionPriority.NORMAL)

                # 更新状态显示
                self.motion_label.setText(f'动画: {motion_name} ✓')
                self.motion_label.setStyleSheet("""
                    QLabel {
                        color: lime;
                        background-color: rgba(0, 100, 0, 100);
                        border-radius: 3px;
                        padding: 3px;
                    }
                """)

                print(f"播放随机动画: {motion_name}")

            except Exception as e:
                print(f"播放动画失败: {e}")
                self.motion_label.setText(f'动画: 播放失败 ✗')
                self.motion_label.setStyleSheet("""
                    QLabel {
                        color: red;
                        background-color: rgba(100, 0, 0, 100);
                        border-radius: 3px;
                        padding: 3px;
                    }
                """)

    def resizeGL(self, width: int, height: int):
        if self.model:
            self.model.Resize(width, height)

    def toggle_transparency(self):
        """切换窗口透明度"""
        if self.is_transparent:
            # 切换到不透明
            self.setAttribute(Qt.WA_TranslucentBackground, False)
            self.setStyleSheet("background-color: rgba(50, 50, 50, 200);")
            self.toggle_btn.setText('🌟')
            self.is_transparent = False
        else:
            # 切换到透明
            self.setAttribute(Qt.WA_TranslucentBackground, True)
            self.setStyleSheet("")
            self.toggle_btn.setText('🔄')
            self.is_transparent = True

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPos() - self.frameGeometry().topLeft()
            event.accept()

    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton:
            self.move(event.globalPos() - self.drag_position)
            event.accept()

if __name__ == '__main__':
    try:
        # 设置默认OpenGL格式 - 在创建QApplication之前
        format = QSurfaceFormat()
        format.setAlphaBufferSize(8)
        format.setDepthBufferSize(24)
        format.setStencilBufferSize(8)
        format.setSamples(0)  # 禁用抗锯齿
        format.setVersion(2, 1)
        format.setProfile(QSurfaceFormat.CompatibilityProfile)
        QSurfaceFormat.setDefaultFormat(format)

        live2d.init()
        app = QApplication(sys.argv)
        window = HiyoriTransparentWindow()
        window.show()

        print("Hiyori透明窗口已启动!")
        print("- 🔄 蓝色按钮：切换透明/半透明背景")
        print("- ✕ 红色按钮：关闭窗口")
        print("- 拖拽模型：移动窗口")

        sys.exit(app.exec_())
    except Exception as e:
        print(f"Error: {e}")
    finally:
        live2d.dispose()
